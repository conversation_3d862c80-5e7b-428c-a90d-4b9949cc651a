﻿--[[
 金庸群侠传X外接脚本公用配置文件
 汉家松鼠工作室(http://www.jy-x.com)
]]--

local Debug = luanet.import_type('UnityEngine.Debug')
local Color = luanet.import_type('UnityEngine.Color')
local Tools = luanet.import_type('JyGame.Tools')
local LuaTool = luanet.import_type('JyGame.LuaTool')
local AudioManager = luanet.import_type('JyGame.AudioManager')
local logger = luanet.import_type('JyGame.FileLogger').instance
local RuntimeData = luanet.import_type('JyGame.RuntimeData')

--载入的LUA文件列表
function ROOT_getLuaFiles()
	return {
		"rollrole.lua",
		"triggerlogic.lua",
		"AttackLogic.lua",
		"GameEngine.lua",
		"AI.lua",
		"battle.lua",
		"skill.lua",
		"buff.lua",
		"item.lua",
	}
end

--资源载入完成
--这里一般用于做数据调试和测试
function ROOT_onInitedResources()
	logger:Log("game started..")
end

--配置列表
function ROOT_getConfigList()
	return {
		--主菜单音乐（默认："音乐.姻缘"）
		MAINMENU_MUSIC = "音乐.姻缘",
		
		--主菜单背景图（默认："地图.开始画面2"）
		MAINMENU_BG =LuaTool.MakeStringArray({"地图.开始画面2"}),
	
		--开场剧本（默认："新手村_出生"）
		gamestart_story = "新手村_出生",	
		
		--开场地点（默认："南贤居"）
		gamestart_location = "主角房",
		
		--每周目敌人增加攻击力比例（默认：0.2）
		ZHOUMU_ATTACK_ADD = 0.35,
		
		--每周目敌人增加的防御力比例（默认：0.2）
		ZHOUMU_DEFENCE_ADD = 0.3,
		
		--每周目敌人增加血量比例（默认：0.1）
		ZHOUMU_HP_ADD = 0.2,
		
		--每周目敌人增加内力比例（默认：0.1）
		ZHOUMU_MP_ADD = 0.2,
		
		--多少个周目提高一级武功等级上限（默认：2）
		PER_MAXLEVEL_ADD_BY_ZHOUMU = 400,
		
		--NPC多少个周目提高所有武功等级1级（默认：2）
		NPC_SKILL_LEVEL_ADD_BY_ZHOUMU = 400,
		
		--默认最大战斗时间（默认：3000）
		DEFAULT_MAX_GAME_SPTIME = 3000,
		
		--是否在游戏中开启控制台（默认：true）
		CONSOLE = true,
		
		--最大内功数量（默认：10）
		MAX_INTERNALSKILL_COUNT = 10,
		
		--最大外功数量（默认：15）
		MAX_SKILL_COUNT = 15,
		
		--最大可分点属性值（默认：1000）
		MAX_ATTRIBUTE = 1000,
		
		--最大外功等级（默认：20）
		MAX_SKILL_LEVEL = 20,
		
		--最大内功等级（默认：20）
		MAX_INTERNALSKILL_LEVEL = 20,
		
		--战斗元宝掉率(2%)
		YUANBAO_DROP_RATE = 0.02,
		
		--最大血内上限（默认：100000）
		MAX_HPMP = 100000,
		
		--每周目增长血内上限（默认：10000）
		MAX_HPMP_PER_ROUND = 10000,
		
		--人物等级上限（默认：30）
		MAX_LEVEL = 30,
		
		--箱子物品上限（默认：10 + RuntimeData.Instance.Round * 5）
		MAX_XIANGZI_ITEM_COUNT = 5 + RuntimeData.Instance.Round * 5,

		--困难残章掉率(2%，默认：0.02)
		HARD_MODE_CANZHANG_DROPRATE = 0.02,
		
		--炼狱以上难度残章基础掉率(5%，默认：0.05)
		CRAZY_MODE_CANZHANG_DROPRATE = 0.05;
		
		--炼狱以上难度残章基础掉率每周目递增(0.5%，默认：0.01)
		CRAZY_MODE_CANZHANG_DROPRATE_PER_ROUND = 0.01,
		
		--外功最大掉落残章的武学难度值(不含，默认：8)
		CANZHANG_MAX_HARD_SKILL = 8,
		
		--内功最大掉落残章的武学难度值(不含，默认：8)
		CANZHANG_MAX_HARD_INTERNALSKILL = 8,
		
		--外功残章掉率是内功的多少倍（默认：2.0）
		CANZHANG_DROP_RATE_INTERNAL_RATE = 2.0,
		
		--随机战斗音乐
		randomBattleMusics = LuaTool.MakeStringArray({
            "音乐.朝云出击", 
            "音乐.危时仗剑",   
            "音乐.道法无边", 
            "音乐.势如破竹",    
	    "音乐.苍龙抱云",       

		}),
		
		--困难难度 NPC随机天赋池
		EnemyRandomTalentsList = LuaTool.MakeStringArray({
			"飘然",
            "斗魂",
            "哀歌",
            "奋战",
            "老江湖",
            "百足之虫",
            "伤口撕裂",
            "暴躁",
            "金钟罩",
            "鲁莽",
            "刀封印",
            "剑封印",
            "奇门封印",
            "拳掌封印",
            "自我主义",
            "清心",
            "破甲",
            "好色",
            "嗜酒如命",
            "破甲",
            "左手剑",
            "右臂有伤",
            "拳掌增益",
            "剑法增益",
            "刀法增益",
            "奇门增益",
            "锐眼"
		}),
		
		--说明：炼狱、无悔难度是每次从以下3个天赋池中各取一个
		--炼狱、无悔难度下NPC随机天赋池1
		EnemyRandomTalentListCrazy1 = LuaTool.MakeStringArray({
			"百足之虫",
            "金钟罩",
            "九玄碧落",
            "老江湖",
            "暴躁",
            "灵心慧质",
            "精打细算",
            "琴胆剑心",
            "神行百变",
            "神经病",
            "鲁莽",
		}),
		
		--炼狱、无悔难度下NPC随机天赋池2
		EnemyRandomTalentListCrazy2 = LuaTool.MakeStringArray({
			"斗魂",
            "奋战",
            "飘然",
            "自我主义",
            "破甲",
            "铁拳无双",
            "素心神剑",
            "左右互搏",
            "博览群书",
            "阴谋家",
            "琴胆剑心",
            "追魂",
            "铁口直断",
            "左手剑",
            "倚天屠龙",
            "兔起鹘落",
            "寒冰真气",
            "神拳无敌",
            "锐眼"
		}),
		
		--炼狱、无悔难度下NPC随机天赋池3
		EnemyRandomTalentListCrazy3 = LuaTool.MakeStringArray({
			"刀封印",
            "剑封印",
            "奇门封印",
            "拳掌封印",
            "清心",
            "哀歌",
            "幽居",
            "金刚",
            "嗜血狂魔",
            "清风",
            "御风",
            "追魂",
            "诸般封印",
            "内功大师",
            "生机勃勃",
		}),
		
		--同福客栈小游戏最大提升到的属性值（默认：70）
		SMALLGAME_MAX_ATTRIBUTE = 30,
		
		--奥义呐喊（女声）
		AOYI_SOUND_FEMALE = LuaTool.MakeStringArray({
			"音效.女", "音效.女2", "音效.女3", "音效.女4"
		}),
		
		--奥义呐喊（男声）
		AOYI_SOUND_MALE = LuaTool.MakeStringArray({
			"音效.男", "音效.男2", "音效.男3", "音效.男4", "音效.男5", "音效.男-哼"
		}),
		
		--奥义音效（随机选取）
		AOYI_EFFECT = LuaTool.MakeStringArray({
			"音效.内功攻击4", "音效.打雷", "音效.奥义1", "音效.奥义2", "音效.奥义3", "音效.奥义4", "音效.奥义5", "音效.奥义6"
		}),
		
		--珍珑棋局随机掉落武器
		ZHENLONG_WUQI = LuaTool.MakeStringArray({
			 "红莲劫焰","幻剑煌熇","冰魄云渺","瑶光绫索","碧玉刀","长生剑","多情环","孔雀翎"
		}),
		
		--珍珑棋局防具
		ZHENLONG_FANGJU = LuaTool.MakeStringArray({
			"乌蚕衣","岳飞的重铠","瑶池仙袖","胧华千纱","珠璃莹甲"
		}),
		
		--珍珑棋局饰品
		ZHENLONG_SHIPIN = LuaTool.MakeStringArray({
			"铂金戒指","蓝宝戒指"
		}),
		
		--BUFF列表
		BUFF_LIST = LuaTool.MakeStringArray({
			"恢复", "集气", "攻击强化", "飘渺", "左右互搏", "神速攻击", "醉酒", "溜须拍马", "易容", 
            	"狂战", "坚守", "沾衣十八跌", "圣战", "轻身", "防御强化", "魔神降临", "神行", "反击", "心眼",
	    	"闪避", "吸星", "嗜血", "金身", "毒人", "魅惑", "落英", "恒山剑意", "衡山剑意", "泰山剑意",
		"华山剑意", "嵩山剑意", "静心"
		}),
		
		--DEBUFF列表
		DEBUFF_LIST = LuaTool.MakeStringArray({
			"中毒", "内伤", "致盲", "缓速", "晕眩", "攻击弱化", "诸般封印", "剑封印", "刀封印", 
            "拳掌封印", "奇门封印", "伤害加深", "重伤", "定身", "封穴", "点穴", "诅咒", "流血"
		}),
		
		--游戏战斗加速倍速（默认：1.5）
		BATTLE_SPEEDUP_RATE = 1.4,
		
		--战斗场景X坐标格数（默认：11）
		BATTLE_MOVEBLOCK_MAX_X = 14,

		--战斗场景Y坐标格数（默认：4）
		BATTLE_MOVEBLOCK_MAX_Y = 4,

		--战斗场景格子长度（默认：80）
		BATTLE_MOVEBLOCK_LENGTH = 64,

		--战斗场景格子宽度（默认：80）
		BATTLE_MOVEBLOCK_WIDTH = 64,

		--战斗场景右边距（默认：5，单位：格子数）
		BATTLE_MOVEBLOCK_MARGIN_RIGHT = 7.0,

		--战斗场景上边距（默认：2，单位：格子数）
		BATTLE_MOVEBLOCK_MARGIN_TOP = 2.4,

		--战斗场景格子大小修正（默认：1.25，单位：倍数）
		--注意：格子的实际大小为64*64，但金X默认值为1.25倍（即80*80）
		BATTLE_MOVEBLOCK_SCALE = 1.0,
		--战斗场景格子移除（10格以内）（默认为""）。设置格式：x1,y1,注释（可选）#x2,y2,注释（可选）#x3,y3,注释（可选），例如：0,4#11,4,右上角#11,0,右下角
		BATTLE_MOVEBLOCK_REMOVE = "",
		
		--预加载图片：1开启，0关闭（默认：0）
		PRECACHE_TEXTURE = 0,
		
		--游戏帧率FPS：20--60之间（默认：60）
		FRAME_RATE = 60,
		
		--技能施放延时系数：-1.0--0.1之间（默认-1.0）
		SKILL_DELAY_COEFFICIENT = 0.01,
		
		--加密存档：0不加密，1加密
		ENCRYPT_SAVE = 0,
		
		--BUFF和技能CD时间：10--100之间（默认50）
		BUFF_SKILL_CD = 50

	}
end